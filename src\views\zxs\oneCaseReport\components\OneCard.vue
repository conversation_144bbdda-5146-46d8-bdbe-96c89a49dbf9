<template>
  <div class="w-full relative">
    <div class="w-100vw h-156px bg-top-bg"></div>
    <div class="welcome-text">
      <div class="mb-6px px">Hi {{ reportData?.studentName || '无学生' }}</div>
      <div>{{ reportData?.sysSubjectName }}测试报告</div>
    </div>
    <div
      class="card-box card mx-12px px-12px pb-19px relative rounded-[8px] top-[-10px]"
    >
      <div class="one-icon"></div>
      <div class="chart-container pt-25px relative">
        <div class="chart-wrapper">
          <g-chart :option="chartOption" class="w-full h-[182px] mx-auto" />
        </div>
        <!-- 等级问号图标 -->
        <g-icon
          name="ri-question-fill"
          size="14"
          color="#D8D8D8"
          class="absolute cursor-pointer translate-x-[calc(-50%+26px)]"
          style="left: 50%; top: 68%; transform: translateX(calc(-50% + 24px))"
          @click="showLevelDialog"
        />
      </div>
      <div class="ms-card mx-">
        <img
          :src="$g.tool.getFileUrl(`oneCaseReport/${reportData?.rating}.png`)"
          alt=""
          class="w-20px h-20px mr-4px inline-block"
        />
        {{ reportData?.ratingContent || '无评价内容' }}
      </div>
    </div>

    <LevelDialog v-model:show="levelDialogShow" />
  </div>
</template>

<script setup lang="ts">
import LevelDialog from './LevelDialog.vue'
import type { IOneCaseReportDetail } from '../type'

/** 组件Props接口 */
interface IProps {
  /** 报告数据 */
  reportData?: IOneCaseReportDetail | null
}

/** 定义Props */
let props = withDefaults(defineProps<IProps>(), {
  reportData: null,
})

/** 根据分数获取评级颜色和数字颜色 */
function getRatingColor(rating: string): {
  gradient: [string, string]
  textColor: string
  bgColor: string
} {
  switch (rating) {
    case '很好':
      return {
        gradient: ['#53E59B', '#2CAD42'],
        textColor: '#00DA8F',
        bgColor: '#ECFBEC',
      }
    case '好':
      return {
        gradient: ['#53E59B', '#2CAD42'],
        textColor: '#00DA8F',
        bgColor: '#ECFBEC',
      }
    case '一般':
      return {
        gradient: ['#A2B0FF', '#7585FD'],
        textColor: '#6A7AFD',
        bgColor: '#EAECFF',
      }
    case '较差':
      return {
        gradient: ['#FFB126', '#F83939'],
        textColor: '#FF4646',
        bgColor: '#FEEDDB',
      }
    case '差':
      return {
        gradient: ['#FFB126', '#F83939'],
        textColor: '#FF4646',
        bgColor: '#FEEDDB',
      }
    default:
      return {
        gradient: ['#A2B0FF', '#7585FD'],
        textColor: '#6A7AFD',
        bgColor: '#EAECFF',
      }
  }
}

/** 获取评级显示文本 */
const getRatingText = computed(() => {
  const title = props.reportData?.ratingTitle || '一般'
  return `评级·${title}`
})

/** 圆环图配置 */
const chartOption = computed(() => {
  const currentRating = props.reportData?.ratingTitle || '一般'
  const currentScore = props.reportData?.finallyScore || 0
  const colors = getRatingColor(currentRating)

  // 使用项目的像素适配方法，needUnit=false返回纯数字
  const ringWidth = $g.tool.pxConversionAdaptedPx(21, false) // 圆环粗细21px
  const mainFontSize = $g.tool.pxConversionAdaptedPx(35, false) // 数字35px
  const unitFontSize = $g.tool.pxConversionAdaptedPx(17, false) // "分"17px
  const titleFontSize = $g.tool.pxConversionAdaptedPx(12, false) // 评级12px

  return {
    // 确保图表铺满整个容器
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      containLabel: false,
    },
    series: [
      {
        type: 'gauge',
        min: 0,
        max: 150,
        splitNumber: 12,
        radius: '100%',
        center: ['50%', '50%'], // 居中位置
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: colors.gradient[0],
              },
              {
                offset: 1,
                color: colors.gradient[1],
              },
            ],
          },
        },
        progress: {
          show: true,
          roundCap: true,
          width: ringWidth,
        },
        pointer: {
          show: false,
        },
        axisLine: {
          roundCap: true,
          lineStyle: {
            width: ringWidth,
            color: [[1, colors.bgColor]],
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        title: {
          show: true,
          offsetCenter: [0, '35%'],
          fontSize: titleFontSize,
          color: '#999',
          fontWeight: 'normal',
        },
        detail: {
          width: 50,
          height: 14,
          fontSize: mainFontSize,
          color: colors.textColor,
          backgroundColor: 'transparent',
          borderRadius: 3,
          formatter: function (value: number) {
            const formattedValue =
              value % 1 === 0 ? value.toString() : value.toFixed(1)
            return `{value|${formattedValue}}{unit|分}`
          },
          rich: {
            value: {
              fontSize: mainFontSize,
              fontWeight: 'bold',
              color: colors.textColor,
            },
            unit: {
              fontSize: unitFontSize,
              color: colors.textColor,
              padding: [11, 0, 0, 2],
            },
          },
          offsetCenter: [0, '-15%'],
        },
        data: [
          {
            value: currentScore,
            name: getRatingText.value,
          },
        ],
      },
    ],
  }
})

/** 等级弹窗显示状态 */
let levelDialogShow = $ref(false)

/** 显示等级弹窗 */
function showLevelDialog(): void {
  levelDialogShow = true
}
</script>

<style lang="scss" scoped>
.bg-top-bg {
  background: url('@/assets/img/oneCaseReport/top-bg.png') no-repeat center
    center;
  background-size: 100% 100%;
}

.card {
  background: url('@/assets/img/oneCaseReport/one-card.png') no-repeat right top;
  margin-bottom: 5px;
  background-size: cover;
}

.welcome-text {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  color: #fff;
  font-size: 25px;
  font-weight: 600;
  left: 21px;
  top: 50px;
}

.one-icon {
  width: 113px;
  height: 123px;
  height: 100px;
  background: url('@/assets/img/oneCaseReport/one-icon.png') no-repeat center
    center;
  background-size: 100% 100%;
  position: absolute;
  right: 10px;
  top: -65px;
  z-index: 1;
}

.pfdj {
  width: 88px;
  height: 26px;
  background: url('@/assets/img/oneCaseReport/pfdj.png') no-repeat center center;
  background-size: 100% 100%;
}

.ysya-text {
  font-family: 'AlimamaShuHeiTi-Ysya', sans-serif;
  font-size: 17px;
  position: relative;
  top: -6px;
}

.chart-container {
  .chart-wrapper {
    position: relative;
    margin: 0 auto; // 居中显示
  }

  .label {
    font-size: 10px;
    text-align: center;
  }
}

.ms-card {
  background: linear-gradient(90deg, #ffffff 0%, #eff7ff 100%);
  border-radius: 12px;
  border: 2px solid #eeeeee;
  padding: 12px;
  // height: 100px;
}
</style>
